<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="2e651435-f608-4a9e-8570-bf89517ab024" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "amenti4k"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/dideroai/didero-api.git",
    "accountId": "8db3155e-a5bd-43e5-a91a-af7774771e33"
  }
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 8
}]]></component>
  <component name="ProjectId" id="31RgTh25BZ1xuL8bFXJwhKZDQTJ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "researching"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2e651435-f608-4a9e-8570-bf89517ab024" name="Changes" comment="" />
      <created>1755492309287</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755492309287</updated>
    </task>
    <servers />
  </component>
</project>