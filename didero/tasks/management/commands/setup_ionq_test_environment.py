"""
Management command to set up local test environment mirroring IonQ production.
Creates team, users, suppliers, and ERP configuration matching production data.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from datetime import date
from decimal import Decimal

from didero.users.models import Team
from didero.users.models.team_models import TeamDomain
from didero.suppliers.models import Supplier
from didero.orders.models import PurchaseOrder, Shipment, OrderAcknowledgement, LineItem
from didero.integrations.models import ERPIntegrationConfig
from didero.tasks.models import TaskActionType

User = get_user_model()


class Command(BaseCommand):
    help = "Set up local test environment mirroring IonQ production configuration"

    def add_arguments(self, parser):
        parser.add_argument(
            "--reset",
            action="store_true",
            help="Reset existing data before creating new test environment",
        )
        parser.add_argument(
            "--create-test-data",
            action="store_true",
            help="Create test POs and shipments for testing",
        )

    def handle(self, *args, **options):
        if options["reset"]:
            self.stdout.write("🧹 Resetting existing test data...")
            self.reset_test_data()

        self.stdout.write("🏗️  Setting up IonQ test environment...")

        # Step 1: Create team
        team = self.create_ionq_team()

        # Step 2: Create users
        users = self.create_test_users(team)

        # Step 3: Create suppliers
        suppliers = self.create_suppliers(team)

        # Step 4: Create ERP configuration
        erp_config = self.create_erp_configuration(team)

        # Step 5: Create task action types
        self.create_task_action_types()

        # Step 6: Optionally create test data
        if options["create_test_data"]:
            self.create_test_purchase_orders(team, users, suppliers)

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ IonQ test environment created successfully!\n"
                f"   Team: {team.name} (ID: {team.id})\n"
                f"   Users: {len(users)} created\n"
                f"   Suppliers: {len(suppliers)} created\n"
                f"   ERP Config: {erp_config.erp_type} configured"
            )
        )

    def reset_test_data(self):
        """Reset existing test data."""
        # Delete test teams (be careful not to delete production data)
        test_domains = TeamDomain.objects.filter(domain__in=["ionq.co", "test-ionq.co"])
        for team_domain in test_domains:
            team = team_domain.team
            self.stdout.write(f"   Deleting team: {team.name}")
            team.delete()  # This will cascade delete the TeamDomain

    def create_ionq_team(self):
        """Create IonQ team matching production configuration."""
        # First check if team with this domain already exists
        try:
            team_domain = TeamDomain.objects.get(domain="test-ionq.co")
            team = team_domain.team
            self.stdout.write(f"   ♻️  Using existing team: {team.name}")
        except TeamDomain.DoesNotExist:
            # Create new team
            team = Team.objects.create(name="IonQ Test Environment")

            # Create team domain
            TeamDomain.objects.create(team=team, domain="test-ionq.co")

            self.stdout.write(f"   ✅ Created team: {team.name}")

        return team

    def create_test_users(self, team):
        """Create test users for the team."""
        users = []

        # Create primary test user
        user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Test",
                "last_name": "User",
            },
        )
        users.append(user)

        if created:
            self.stdout.write(f"   ✅ Created user: {user.email}")
        else:
            self.stdout.write(f"   ♻️  Using existing user: {user.email}")

        # Create Didero AI user for the team
        ai_user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Didero",
                "last_name": "AI",
            },
        )
        users.append(ai_user)

        if created:
            self.stdout.write(f"   ✅ Created AI user: {ai_user.email}")
        else:
            self.stdout.write(f"   ♻️  Using existing AI user: {ai_user.email}")

        return users

    def create_suppliers(self, team):
        """Create suppliers matching production data."""
        suppliers = []

        supplier_names = [
            "The Indium Corporation",
            "nVent Schroff",
            "Test Supplier A",
            "Test Supplier B",
        ]

        for name in supplier_names:
            supplier, created = Supplier.objects.get_or_create(name=name, team=team)
            suppliers.append(supplier)

            if created:
                self.stdout.write(f"   ✅ Created supplier: {supplier.name}")
            else:
                self.stdout.write(f"   ♻️  Using existing supplier: {supplier.name}")

        return suppliers

    def create_erp_configuration(self, team):
        """Create ERP configuration matching production NetSuite setup."""
        config, created = ERPIntegrationConfig.objects.get_or_create(
            team=team,
            erp_type="netsuite",
            defaults={
                "enabled": True,
                "field_mappings": {
                    # Production field mappings from investigation
                    "tracking_number": "custbody_ionq_tracking_number",
                    "promised_ship_date": "custcol_ionq_supplierpromisedatefield",
                    "estimated_delivery_date": "expectedreceiptdate",
                },
                "config": {
                    # Sandbox credentials for testing
                    "account_id": "7581852_SB1",
                    "environment": "sandbox",
                    "api_version": "2023_2",
                    "authentication_type": "oauth1",
                    # Test credentials (would be real in production)
                    "consumer_key": "test_consumer_key",
                    "consumer_secret": "test_consumer_secret",
                    "token_id": "test_token_id",
                    "token_secret": "test_token_secret",
                },
            },
        )

        if created:
            self.stdout.write(f"   ✅ Created ERP config: {config.erp_type}")
        else:
            self.stdout.write(f"   ♻️  Using existing ERP config: {config.erp_type}")

        return config

    def create_task_action_types(self):
        """Create required task action types."""
        action_types = [
            ("CONFIRM_SHIPMENT", "Confirm Shipment", "Confirm shipment in ERP system"),
            ("CANCEL_SHIPMENT", "Cancel Shipment", "Cancel shipment in ERP system"),
            (
                "CONFIRM_ORDER_ACKNOWLEDGEMENT",
                "Confirm Order Acknowledgement",
                "Confirm order acknowledgement in ERP system",
            ),
            (
                "CANCEL_ORDER_ACKNOWLEDGEMENT",
                "Cancel Order Acknowledgement",
                "Cancel order acknowledgement in ERP system",
            ),
        ]

        for name, title, sub_text in action_types:
            action_type, created = TaskActionType.objects.get_or_create(
                name=name,
                defaults={"title_template": title, "sub_text_template": sub_text},
            )

            if created:
                self.stdout.write(f"   ✅ Created task action type: {name}")
            else:
                self.stdout.write(f"   ♻️  Using existing task action type: {name}")

    def create_test_purchase_orders(self, team, users, suppliers):
        """Create test purchase orders matching production scenarios."""
        self.stdout.write("📦 Creating test purchase orders...")

        user = users[0]  # Primary test user
        indium_supplier = next(s for s in suppliers if "Indium" in s.name)
        nvent_supplier = next(s for s in suppliers if "nVent" in s.name)

        # Test scenario 1: PO12302 equivalent (received with shipment)
        po1, created = PurchaseOrder.objects.get_or_create(
            po_number="TEST-PO12302",
            team=team,
            defaults={
                "supplier": indium_supplier,
                "placed_by": user,
                "order_status": "received",
                "source": "manual",
                "placement_time": date(2025, 8, 1),
            },
        )

        shipment1 = Shipment.objects.create(
            purchase_order=po1,
            shipment_date=date(2025, 8, 8),
            estimated_delivery_date=date(2025, 8, 8),
            tracking_number="TEST-1Z2W50450338174329",
            carrier="UPS",
        )

        LineItem.objects.create(
            purchase_order=po1,
            category="custom",
            description="501-00115 - Test Indium Component (40.0 units)",
            amount=Decimal("1000.00"),
        )

        self.stdout.write(f"   ✅ Created PO: {po1.po_number} with shipment")

        # Test scenario 2: PO11410 equivalent (awaiting shipment)
        po2 = PurchaseOrder.objects.create(
            po_number="TEST-PO11410",
            team=team,
            supplier=nvent_supplier,
            placed_by=user,
            order_status="awaiting_shipment",
            source="manual",
            placement_time=date(2025, 8, 1),
        )

        # Create multiple line items
        for i in range(1, 5):
            LineItem.objects.create(
                purchase_order=po2,
                category="custom",
                description=f"501-01000 - 4U cPCI-S chassis (Unit {i})",
                amount=Decimal("500.00"),
            )

        self.stdout.write(f"   ✅ Created PO: {po2.po_number} awaiting shipment")

        # Test scenario 3: PO with order acknowledgement
        po3 = PurchaseOrder.objects.create(
            po_number="TEST-PO-OA",
            team=team,
            supplier=suppliers[2],  # Test Supplier A
            placed_by=user,
            order_status="acknowledged",
            source="manual",
            placement_time=date(2025, 8, 1),
        )

        oa = OrderAcknowledgement.objects.create(
            purchase_order=po3,
            acknowledgement_date=date(2025, 8, 10),
            promised_ship_date=date(2025, 8, 15),
        )

        LineItem.objects.create(
            purchase_order=po3,
            category="custom",
            description="TEST-ITEM-001 - Test Component with OA (10.0 units)",
            amount=Decimal("750.00"),
        )

        self.stdout.write(
            f"   ✅ Created PO: {po3.po_number} with order acknowledgement"
        )

        self.stdout.write(
            self.style.SUCCESS(
                f"📦 Test data created:\n"
                f"   - {po1.po_number}: Received with shipment (ready for ERP sync)\n"
                f"   - {po2.po_number}: Awaiting shipment (4 line items)\n"
                f"   - {po3.po_number}: With order acknowledgement"
            )
        )
