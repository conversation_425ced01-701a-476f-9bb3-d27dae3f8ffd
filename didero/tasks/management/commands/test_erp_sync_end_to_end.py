"""
End-to-end test script for ERP sync workflow.
Tests complete flow from task creation through execution for IonQ scenarios.
"""

import asyncio
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from unittest.mock import Mock, patch, AsyncMock

from didero.users.models import Team
from didero.orders.models import PurchaseOrder, Shipment, OrderAcknowledgement
from didero.tasks.models import Task, TaskAction
from didero.tasks.actions import confirm_shipment, confirm_order_acknowledgement
from didero.integrations.models import ERPIntegrationConfig
from didero.integrations.erp.field_mapper import NetSuiteFieldMapper
from didero.workflows.core.activities.erp_sync import (
    get_erp_notification_recipient_for_po,
    get_erp_notification_recipient_guaranteed,
)

User = get_user_model()


class Command(BaseCommand):
    help = "Run end-to-end tests for ERP sync workflow"

    def add_arguments(self, parser):
        parser.add_argument(
            "--scenario",
            choices=[
                "all",
                "shipment",
                "oa",
                "task-creation",
                "field-mapping",
                "execution",
            ],
            default="all",
            help="Which test scenario to run",
        )
        parser.add_argument(
            "--team-domain",
            default="test-ionq.co",
            help="Team domain to use for testing",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Enable verbose output",
        )

    def handle(self, *args, **options):
        self.verbose = options["verbose"]
        self.team_domain = options["team_domain"]

        self.stdout.write("🧪 Starting ERP Sync End-to-End Tests")
        self.stdout.write("=" * 50)

        # Get test team
        try:
            self.team = Team.objects.get(domain=self.team_domain)
            self.stdout.write(f"📋 Using team: {self.team.name} (ID: {self.team.id})")
        except Team.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(
                    f"❌ Team with domain '{self.team_domain}' not found. "
                    f"Run 'python manage.py setup_ionq_test_environment' first."
                )
            )
            return

        scenario = options["scenario"]

        if scenario in ["all", "task-creation"]:
            self.test_task_creation()

        if scenario in ["all", "field-mapping"]:
            self.test_field_mapping()

        if scenario in ["all", "shipment"]:
            self.test_shipment_sync_flow()

        if scenario in ["all", "oa"]:
            self.test_order_acknowledgement_flow()

        if scenario in ["all", "execution"]:
            self.test_task_execution()

        self.stdout.write(self.style.SUCCESS("✅ All ERP sync tests completed!"))

    def test_task_creation(self):
        """Test ERP task creation functionality."""
        self.stdout.write("\n🏗️  Testing ERP Task Creation")
        self.stdout.write("-" * 30)

        # Get test POs
        test_pos = PurchaseOrder.objects.filter(
            team=self.team, po_number__startswith="TEST-"
        )

        if not test_pos.exists():
            self.stdout.write(
                self.style.WARNING(
                    "⚠️  No test POs found. Run with --create-test-data first."
                )
            )
            return

        from didero.tasks.management.commands.create_erp_tasks import (
            Command as CreateERPTasksCommand,
        )

        for po in test_pos:
            self.stdout.write(f"   Testing PO: {po.po_number}")

            # Test task creation
            command = CreateERPTasksCommand()

            # Determine what tasks should be created
            has_shipment = hasattr(po, "shipment") and po.shipment
            has_oa = po.orderacknowledgement_set.exists()

            if has_shipment:
                tasks = command.create_tasks_for_po(po, task_types=["shipment"])
                self.stdout.write(f"     ✅ Created {len(tasks)} shipment task(s)")

                if self.verbose:
                    for task in tasks:
                        actions = task.actions.all()
                        self.stdout.write(f"        Task: {task.name}")
                        self.stdout.write(
                            f"        Actions: {[a.action_type.name for a in actions]}"
                        )

            if has_oa:
                tasks = command.create_tasks_for_po(po, task_types=["oa"])
                self.stdout.write(f"     ✅ Created {len(tasks)} OA task(s)")

    def test_field_mapping(self):
        """Test ERP field mapping functionality."""
        self.stdout.write("\n🗺️  Testing ERP Field Mapping")
        self.stdout.write("-" * 30)

        # Get ERP config
        try:
            erp_config = ERPIntegrationConfig.objects.get(team=self.team)
        except ERPIntegrationConfig.DoesNotExist:
            self.stdout.write(
                self.style.ERROR("❌ No ERP configuration found for team")
            )
            return

        # Test shipment field mapping
        shipments = Shipment.objects.filter(purchase_order__team=self.team)

        if shipments.exists():
            shipment = shipments.first()
            self.stdout.write(f"   Testing shipment: {shipment.id}")

            mapper = NetSuiteFieldMapper(erp_config)
            mapped_data = mapper.prepare_shipment_data(shipment)

            # Validate mapped data
            required_fields = [
                "tracking_number",
                "estimated_delivery_date",
                "line_items",
            ]
            for field in required_fields:
                if field in mapped_data:
                    self.stdout.write(f"     ✅ {field}: {mapped_data[field]}")
                else:
                    self.stdout.write(f"     ❌ Missing field: {field}")

            if self.verbose:
                self.stdout.write(f"     Full mapped data: {mapped_data}")

        # Test OA field mapping
        oas = OrderAcknowledgement.objects.filter(purchase_order__team=self.team)

        if oas.exists():
            oa = oas.first()
            self.stdout.write(f"   Testing OA: {oa.id}")

            mapper = NetSuiteFieldMapper(erp_config)
            mapped_data = mapper.prepare_order_acknowledgement_data(oa)

            self.stdout.write(f"     ✅ OA mapped data prepared")
            if self.verbose:
                self.stdout.write(f"     Full mapped data: {mapped_data}")

    def test_shipment_sync_flow(self):
        """Test complete shipment sync flow."""
        self.stdout.write("\n🚢 Testing Shipment Sync Flow")
        self.stdout.write("-" * 30)

        # Find shipment with task
        shipment_tasks = Task.objects.filter(
            team=self.team, name__icontains="shipment", status="PENDING"
        )

        if not shipment_tasks.exists():
            self.stdout.write("⚠️  No pending shipment tasks found")
            return

        task = shipment_tasks.first()
        self.stdout.write(f"   Testing task: {task.name}")

        # Get confirm action
        confirm_action = task.actions.filter(
            action_type__name="CONFIRM_SHIPMENT"
        ).first()

        if not confirm_action:
            self.stdout.write("❌ No CONFIRM_SHIPMENT action found")
            return

        self.stdout.write(f"   Found action: {confirm_action.action_type.name}")
        self.stdout.write(f"   Execution params: {confirm_action.execution_params}")

        # Test notification recipient lookup
        shipment_id = confirm_action.execution_params.get("shipment_id")
        if shipment_id:
            try:
                shipment = Shipment.objects.get(id=shipment_id)

                # Test async function
                async def test_recipient():
                    recipient = await get_erp_notification_recipient_guaranteed(
                        shipment
                    )
                    return recipient

                recipient = asyncio.run(test_recipient())
                self.stdout.write(f"   ✅ Notification recipient: {recipient.email}")

            except Shipment.DoesNotExist:
                self.stdout.write("❌ Shipment not found")

    def test_order_acknowledgement_flow(self):
        """Test order acknowledgement sync flow."""
        self.stdout.write("\n📋 Testing Order Acknowledgement Flow")
        self.stdout.write("-" * 30)

        # Find OA with task
        oa_tasks = Task.objects.filter(
            team=self.team, name__icontains="acknowledgement", status="PENDING"
        )

        if not oa_tasks.exists():
            self.stdout.write("⚠️  No pending OA tasks found")
            return

        task = oa_tasks.first()
        self.stdout.write(f"   Testing task: {task.name}")

        # Get confirm action
        confirm_action = task.actions.filter(
            action_type__name="CONFIRM_ORDER_ACKNOWLEDGEMENT"
        ).first()

        if not confirm_action:
            self.stdout.write("❌ No CONFIRM_ORDER_ACKNOWLEDGEMENT action found")
            return

        self.stdout.write(f"   Found action: {confirm_action.action_type.name}")
        self.stdout.write(f"   Execution params: {confirm_action.execution_params}")

        # Test notification recipient lookup
        po_id = confirm_action.execution_params.get("purchase_order_id")
        if po_id:
            try:
                po = PurchaseOrder.objects.get(id=po_id)

                # Test async function
                async def test_recipient():
                    recipient = await get_erp_notification_recipient_for_po(po)
                    return recipient

                recipient = asyncio.run(test_recipient())
                self.stdout.write(f"   ✅ Notification recipient: {recipient.email}")

            except PurchaseOrder.DoesNotExist:
                self.stdout.write("❌ Purchase order not found")

    @patch("didero.tasks.actions.sync_shipment_to_erp_workflow")
    @patch("didero.tasks.actions.sync_order_acknowledgement_to_erp_workflow")
    def test_task_execution(self, mock_oa_workflow, mock_shipment_workflow):
        """Test task action execution with mocked workflows."""
        self.stdout.write("\n⚡ Testing Task Execution")
        self.stdout.write("-" * 30)

        # Mock workflow responses
        mock_shipment_workflow.return_value = AsyncMock()
        mock_oa_workflow.return_value = AsyncMock()

        # Test shipment task execution
        shipment_tasks = Task.objects.filter(
            team=self.team, name__icontains="shipment", status="PENDING"
        )

        if shipment_tasks.exists():
            task = shipment_tasks.first()
            confirm_action = task.actions.filter(
                action_type__name="CONFIRM_SHIPMENT"
            ).first()

            if confirm_action:
                self.stdout.write(f"   Executing shipment action: {confirm_action.id}")

                try:
                    result = confirm_shipment(confirm_action.id)
                    self.stdout.write(f"   ✅ Shipment action executed: {result}")

                    # Verify workflow was called
                    if mock_shipment_workflow.called:
                        self.stdout.write("   ✅ Workflow was triggered")
                    else:
                        self.stdout.write("   ❌ Workflow was not triggered")

                except Exception as e:
                    self.stdout.write(f"   ❌ Execution failed: {str(e)}")

        # Test OA task execution
        oa_tasks = Task.objects.filter(
            team=self.team, name__icontains="acknowledgement", status="PENDING"
        )

        if oa_tasks.exists():
            task = oa_tasks.first()
            confirm_action = task.actions.filter(
                action_type__name="CONFIRM_ORDER_ACKNOWLEDGEMENT"
            ).first()

            if confirm_action:
                self.stdout.write(f"   Executing OA action: {confirm_action.id}")

                try:
                    result = confirm_order_acknowledgement(confirm_action.id)
                    self.stdout.write(f"   ✅ OA action executed: {result}")

                    # Verify workflow was called
                    if mock_oa_workflow.called:
                        self.stdout.write("   ✅ Workflow was triggered")
                    else:
                        self.stdout.write("   ❌ Workflow was not triggered")

                except Exception as e:
                    self.stdout.write(f"   ❌ Execution failed: {str(e)}")
