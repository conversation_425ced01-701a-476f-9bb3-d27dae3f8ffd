"""
Comprehensive integration tests for ERP sync workflow.
Tests the complete flow from task creation through NetSuite integration.
"""

import pytest
from datetime import date, datetime
from decimal import Decimal
from unittest.mock import Mock, patch, AsyncMock
from django.test import TestCase
from django.contrib.auth import get_user_model

from didero.users.models import Team
from didero.suppliers.models import Supplier
from didero.orders.models import PurchaseOrder, Shipment, OrderAcknowledgement, LineItem
from didero.tasks.models import Task, TaskAction, TaskActionType
from didero.tasks.actions import confirm_shipment, confirm_order_acknowledgement
from didero.integrations.models import ERPIntegrationConfig
from didero.integrations.erp.field_mapper import NetSuiteFieldMapper
from didero.workflows.core.activities.erp_sync import (
    get_erp_notification_recipient_for_po,
    get_erp_notification_recipient_guaranteed,
)

User = get_user_model()


class ERPSyncIntegrationTestCase(TestCase):
    """Base test case with common setup for ERP sync tests."""

    def setUp(self):
        """Set up test data mirroring IonQ production environment."""
        # Create IonQ team (ID 173 in production)
        self.team = Team.objects.create(
            name="ionq.co",
            domain="ionq.co",
            default_requestor_email="<EMAIL>",
        )

        # Create test user
        self.user = User.objects.create_user(email="<EMAIL>", username="testuser")

        # Create suppliers from production data
        self.indium_supplier = Supplier.objects.create(
            name="The Indium Corporation", team=self.team
        )

        self.nvent_supplier = Supplier.objects.create(
            name="nVent Schroff", team=self.team
        )

        # Create ERP integration config matching production
        self.erp_config = ERPIntegrationConfig.objects.create(
            team=self.team,
            erp_system="netsuite",
            is_active=True,
            config={
                "tracking_number": "custbody_ionq_tracking_number",
                "promised_ship_date": "custcol_ionq_supplierpromisedatefield",
                "estimated_delivery_date": "expectedreceiptdate",
            },
        )

        # Create task action types
        self.confirm_shipment_type = TaskActionType.objects.get_or_create(
            name="CONFIRM_SHIPMENT",
            defaults={"description": "Confirm shipment in ERP system"},
        )[0]

        self.confirm_oa_type = TaskActionType.objects.get_or_create(
            name="CONFIRM_ORDER_ACKNOWLEDGEMENT",
            defaults={"description": "Confirm order acknowledgement in ERP system"},
        )[0]


class TestERPSyncTaskCreation(ERPSyncIntegrationTestCase):
    """Test ERP sync task creation scenarios."""

    def test_shipment_task_creation_po12302_scenario(self):
        """Test task creation for PO12302 shipment scenario."""
        # Create PO matching production data
        po = PurchaseOrder.objects.create(
            po_number="PO12302",
            team=self.team,
            supplier=self.indium_supplier,
            placed_by=self.user,
            status="received",
        )

        # Create shipment matching production data
        shipment = Shipment.objects.create(
            purchase_order=po,
            shipment_date=date(2025, 8, 8),
            estimated_delivery_date=date(2025, 8, 8),
            tracking_number="1Z2W50450338174329",
            carrier="UPS",
        )

        # Create line item
        LineItem.objects.create(
            purchase_order=po,
            item_number="501-00115",
            quantity=Decimal("40.0"),
            line_number=1,
        )

        # Test task creation
        from didero.tasks.management.commands.create_erp_tasks import Command

        command = Command()

        tasks_created = command.create_tasks_for_po(po, task_types=["shipment"])

        self.assertEqual(len(tasks_created), 1)
        task = tasks_created[0]

        # Verify task structure
        self.assertEqual(task.team, self.team)
        self.assertEqual(task.status, "PENDING")
        self.assertIn("PO12302", task.name)
        self.assertIn("shipment", task.name.lower())

        # Verify task actions
        actions = task.actions.all()
        self.assertEqual(len(actions), 2)  # CONFIRM_SHIPMENT and CANCEL_SHIPMENT

        confirm_action = actions.filter(action_type__name="CONFIRM_SHIPMENT").first()
        self.assertIsNotNone(confirm_action)
        self.assertEqual(
            confirm_action.execution_params["shipment_id"], str(shipment.id)
        )
        self.assertEqual(
            confirm_action.execution_params["purchase_order_id"], str(po.id)
        )


class TestERPSyncFieldMapping(ERPSyncIntegrationTestCase):
    """Test ERP field mapping functionality."""

    def test_netsuite_field_mapper_shipment_data(self):
        """Test NetSuite field mapping for shipment data."""
        # Create test data
        po = PurchaseOrder.objects.create(
            po_number="PO12302",
            team=self.team,
            supplier=self.indium_supplier,
            placed_by=self.user,
        )

        shipment = Shipment.objects.create(
            purchase_order=po,
            shipment_date=date(2025, 8, 8),
            estimated_delivery_date=date(2025, 8, 8),
            tracking_number="1Z2W50450338174329",
            carrier="UPS",
        )

        LineItem.objects.create(
            purchase_order=po,
            item_number="501-00115",
            quantity=Decimal("40.0"),
            line_number=1,
        )

        # Test field mapping
        mapper = NetSuiteFieldMapper(self.erp_config)
        mapped_data = mapper.prepare_shipment_data(shipment)

        # Verify mapped fields
        self.assertEqual(mapped_data["tracking_number"], "1Z2W50450338174329")
        self.assertEqual(mapped_data["estimated_delivery_date"], date(2025, 8, 8))
        self.assertEqual(len(mapped_data["line_items"]), 1)

        line_item = mapped_data["line_items"][0]
        self.assertEqual(line_item.line, 1)
        self.assertEqual(line_item.item_number, "501-00115")
        self.assertEqual(line_item.quantity, Decimal("40.0"))


class TestERPSyncTaskExecution(ERPSyncIntegrationTestCase):
    """Test ERP sync task execution."""

    @patch("didero.tasks.actions.sync_shipment_to_erp_workflow")
    def test_confirm_shipment_action_execution(self, mock_workflow):
        """Test confirm_shipment action execution."""
        # Create test data
        po = PurchaseOrder.objects.create(
            po_number="PO12302",
            team=self.team,
            supplier=self.indium_supplier,
            placed_by=self.user,
        )

        shipment = Shipment.objects.create(
            purchase_order=po,
            shipment_date=date(2025, 8, 8),
            estimated_delivery_date=date(2025, 8, 8),
            tracking_number="1Z2W50450338174329",
        )

        # Create task and action
        task = Task.objects.create(
            team=self.team, name="Test ERP Sync Task", status="PENDING"
        )

        action = TaskAction.objects.create(
            task=task,
            action_type=self.confirm_shipment_type,
            execution_params={
                "shipment_id": str(shipment.id),
                "purchase_order_id": str(po.id),
            },
        )

        # Mock workflow execution
        mock_workflow.return_value = AsyncMock()

        # Execute action
        result = confirm_shipment(action.id)

        # Verify workflow was called
        mock_workflow.assert_called_once()

        # Verify action was marked as executed
        action.refresh_from_db()
        self.assertIsNotNone(action.executed_at)


class TestERPSyncNotificationRecipients(ERPSyncIntegrationTestCase):
    """Test ERP sync notification recipient logic."""

    @pytest.mark.asyncio
    async def test_get_erp_notification_recipient_for_po(self):
        """Test PO-based notification recipient lookup."""
        po = PurchaseOrder.objects.create(
            po_number="PO12302",
            team=self.team,
            supplier=self.indium_supplier,
            placed_by=self.user,
        )

        recipient = await get_erp_notification_recipient_for_po(po)

        # Should return the placed_by user
        self.assertEqual(recipient, self.user)

    @pytest.mark.asyncio
    async def test_get_erp_notification_recipient_guaranteed(self):
        """Test shipment-based notification recipient lookup."""
        po = PurchaseOrder.objects.create(
            po_number="PO12302",
            team=self.team,
            supplier=self.indium_supplier,
            placed_by=self.user,
        )

        shipment = Shipment.objects.create(
            purchase_order=po,
            shipment_date=date(2025, 8, 8),
            estimated_delivery_date=date(2025, 8, 8),
            tracking_number="1Z2W50450338174329",
        )

        recipient = await get_erp_notification_recipient_guaranteed(shipment)

        # Should return the placed_by user
        self.assertEqual(recipient, self.user)


class TestERPSyncEndToEnd(ERPSyncIntegrationTestCase):
    """End-to-end integration tests for complete ERP sync flow."""

    @patch("didero.integrations.erp.registry.ERPClientRegistry.get_client")
    def test_complete_shipment_sync_flow(self, mock_get_client):
        """Test complete shipment sync flow from task creation to NetSuite."""
        # Mock NetSuite client
        mock_client = Mock()
        mock_client.update_purchase_order = AsyncMock(return_value={"success": True})
        mock_get_client.return_value = mock_client

        # Create complete test scenario
        po = PurchaseOrder.objects.create(
            po_number="PO12302",
            team=self.team,
            supplier=self.indium_supplier,
            placed_by=self.user,
            status="received",
        )

        shipment = Shipment.objects.create(
            purchase_order=po,
            shipment_date=date(2025, 8, 8),
            estimated_delivery_date=date(2025, 8, 8),
            tracking_number="1Z2W50450338174329",
            carrier="UPS",
        )

        LineItem.objects.create(
            purchase_order=po,
            item_number="501-00115",
            quantity=Decimal("40.0"),
            line_number=1,
        )

        # Step 1: Create ERP task
        from didero.tasks.management.commands.create_erp_tasks import Command

        command = Command()
        tasks_created = command.create_tasks_for_po(po, task_types=["shipment"])

        self.assertEqual(len(tasks_created), 1)
        task = tasks_created[0]

        # Step 2: Execute task action
        confirm_action = task.actions.filter(
            action_type__name="CONFIRM_SHIPMENT"
        ).first()
        self.assertIsNotNone(confirm_action)

        # This would normally trigger the workflow, but we'll test the components
        # Step 3: Test field mapping
        mapper = NetSuiteFieldMapper(self.erp_config)
        mapped_data = mapper.prepare_shipment_data(shipment)

        # Verify the complete data flow
        self.assertEqual(mapped_data["tracking_number"], "1Z2W50450338174329")
        self.assertEqual(mapped_data["estimated_delivery_date"], date(2025, 8, 8))
        self.assertEqual(len(mapped_data["line_items"]), 1)

        # Verify task is ready for execution
        self.assertEqual(task.status, "PENDING")
        self.assertEqual(
            confirm_action.execution_params["shipment_id"], str(shipment.id)
        )
